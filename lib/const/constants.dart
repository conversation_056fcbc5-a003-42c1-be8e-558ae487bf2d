sealed class DiConstants {
  /*
    - low order registry first
    - default order:
      - unOrdered(0) -> Data -> Domain
        - Data: dio -> interceptor -> service -> repoImpl
        - Domain: mapper -> usecase
  */

  // data
  static const kDataDioOrder = 1;
  static const kDataInterceptorOrder = 2;
  static const kDataServiceOrder = 25;
  static const kDataRepositoryOrder = 30;

  static const kBlocOrder = 50;

  // domain
  static const kDomainMapperOrder = 70;
  static const kDomainHandlerOrder = 71;
  static const kDomainUseCaseOrder = 90;
  static const kDomainPresentationOrder = 93;
  static const kLastestOrder = 99;
}
