import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

const _envVars = ['ENV', 'TEST_VAR'];

final class AppEnv with _AppEnvVars {
  AppEnv._() {
    const env = String.fromEnvironment('ENV');
    this.env = env.toLowerCase();
  }

  static final AppEnv instance = AppEnv._();

  factory AppEnv() => instance;

  String env = '';

  Future init() async {
    const envFile = String.fromEnvironment('ENV_FILE');

    log('env => $env');
    log('envFile => $envFile');

    await dotenv
        .load(fileName: envFile, mergeWith: {'TEST_VAR': '5'})
        .catchError((error) {
          log('Error loading .env file: $error');
        });

    assert(_hasEnoughEnvVars(), 'Missing environment variables');

    if (kReleaseMode) {
      if (!_hasEnoughEnvVars()) {
        throw Exception('Missing environment variables');
      }
    }

    final envVar = dotenv.maybeGet('ENV', fallback: 'dev');
    final testVar = dotenv.maybeGet('TEST_VAR');

    log('envVar => $envVar');
    log('testVar => $testVar');
  }

  bool get isDevEnv => env == 'dev' || env == 'staging';
  bool get isProdEnv => env == 'prod';

  bool _hasEnoughEnvVars() {
    return _envVars.every((varName) {
      final result = dotenv.env.containsKey(varName);

      if (!result) {
        log('Missing environment variable: $varName');
      }

      return result;
    });
  }

  Set<String> get environmentFilters {
    final Set<String> result = {};
    if (isDevEnv) {
      result.add(env);
    } else if (isProdEnv) {
      result.add(env);
    }

    return result;
  }
}

mixin _AppEnvVars {
  String get apiEndPoint => dotenv.get('API_END_POINT');
}
