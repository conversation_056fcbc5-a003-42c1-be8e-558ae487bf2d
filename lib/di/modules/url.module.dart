/*
 * Created Date: 2/01/2024 11:21:11
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 4th July 2024 16:38:26
 * Modified By: gapo
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:injectable/injectable.dart';

import '../../const/app_env.dart';
import '../../const/url/ticket/ticket_url.constants.dart';

@module
abstract class UrlModule {
  @singleton
  @Named('kTicketUrl')
  String get kTicketUrl =>
      '${AppEnv().apiEndPoint}${TicketUrlConstants.kTicketDomain}';
}
