// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:dio/dio.dart' as _i361;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;
import 'package:toannm_tech/data/data.dart' as _i902;
import 'package:toannm_tech/data/data_source/remote/ticket/ticket.service.dart'
    as _i738;
import 'package:toannm_tech/data/repository/ticket_repo_impl.dart' as _i785;
import 'package:toannm_tech/di/modules/url.module.dart' as _i347;
import 'package:toannm_tech/domain/domain.dart' as _i232;
import 'package:toannm_tech/domain/usecase/ticket/ticket_add_follower.usecase.dart'
    as _i683;

extension GetItInjectableX on _i174.GetIt {
  // initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(this, environment, environmentFilter);
    final urlModule = _$UrlModule();
    gh.singleton<String>(
      () => urlModule.kTicketUrl,
      instanceName: 'kTicketUrl',
    );
    gh.lazySingleton<_i738.TicketService>(
      () => _i738.TicketService(
        gh<_i361.Dio>(instanceName: 'kDio'),
        baseUrl: gh<String>(instanceName: 'kTicketUrl'),
      ),
      instanceName: 'kTicketService',
    );
    gh.lazySingleton<_i232.TicketRepository>(
      () => _i785.TicketRepositoryImpl(
        gh<_i902.TicketService>(instanceName: 'kTicketService'),
      ),
      instanceName: 'kTicketRepository',
    );
    gh.factory<_i683.TicketAddFollowerAllStepUseCase>(
      () => _i683.TicketAddFollowerAllStepUseCase(
        gh<_i232.TicketRepository>(instanceName: 'kTicketRepository'),
      ),
    );
    return this;
  }
}

class _$UrlModule extends _i347.UrlModule {}
