/*
 * Created Date: 5/01/2024 17:22:23
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Monday, 21st April 2025 11:38:24
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';

import 'app.component.config.dart';

final GetIt getIt = GetIt.instance;

@InjectableInit(
  externalPackageModulesBefore: [],
  externalPackageModulesAfter: [],
)
Future configureInjection({required Set<String> environmentFilters}) async =>
    getIt.init(environmentFilter: NoEnvOrContainsAny(environmentFilters));
