import 'dart:developer';

import 'package:toannm_tech/di/component/component.dart';

import 'const/app_env.dart';
import 'di/component/app.component.dart' as app;

Future<void> initApp() async {
  await AppEnv().init();

  await app.configureInjection(environmentFilters: AppEnv().environmentFilters);

  final ticketUrl = getIt.get<String>(instanceName: 'kTicketUrl');
  log('ticketUrl -> $ticketUrl');
}
