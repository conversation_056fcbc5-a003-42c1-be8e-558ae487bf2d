// ignore_for_file: public_member_api_docs
/*
 * Created Date: 2/01/2024 11:12:45
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 7th March 2024 10:57:15
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:injectable/injectable.dart';

import '../../const/constants.dart';
import '../../domain/domain.dart';
import '../data.dart';

@LazySingleton(as: TicketRepository, order: DiConstants.kDataRepositoryOrder)
@Named('kTicketRepository')
final class TicketRepositoryImpl implements TicketRepository {
  const TicketRepositoryImpl(
    @Named('kTicketService') this.ticketService,
  );

  final TicketService ticketService;

  @override
  Future<void> addFollowerAllStep({
    required TicketAddFollowerInput followerInput,
  }) {
    return ticketService.addFollowersAllStep(
      ticketId: followerInput.ticketId,
    );
  }
}
