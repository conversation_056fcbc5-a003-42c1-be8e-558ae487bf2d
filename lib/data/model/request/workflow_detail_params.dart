// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'workflow_detail_params.freezed.dart';
part 'workflow_detail_params.g.dart';

@Freezed(
  fromJson: false,
  toJson: true,
)
abstract class WorkFlowDetailParams with _$WorkFlowDetailParams {
  factory WorkFlowDetailParams({
    required String workflowId,
    @Json<PERSON>ey(name: 'with_start_node') @Default(true) bool withStartNode,
  }) = _WorkFlowDetailParams;
}
