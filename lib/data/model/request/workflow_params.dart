// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'workflow_params.freezed.dart';
part 'workflow_params.g.dart';

@Freezed(fromJson: false, toJson: true)
abstract class WorkFlowParams with _$WorkFlowParams {
  factory WorkFlowParams({
    /// 1 -> true, 0 -> false
    @Default(1) int published,

    ///
    @Default(10) int limit,

    ///
    @<PERSON><PERSON><PERSON><PERSON>(name: 'get_of_child_group') @Default(true) bool getOfChildGroup,

    ///
    @<PERSON><PERSON><PERSON><PERSON>(name: 'workflow_group_id') @Default('') String workflowGroupId,

    ///
    @Default('') String q,

    ///
    @<PERSON><PERSON><PERSON><PERSON>(name: 'include_grandchildren')
    @Default(true)
    bool includeGrandchildren,

    ///
    @<PERSON><PERSON><PERSON><PERSON>(name: 'offset') @Default(0) int? offset,
  }) = _WorkFlowParams;
}
