// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'workflow_detail_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$WorkFlowDetailParams {

 String get workflowId;@JsonKey(name: 'with_start_node') bool get withStartNode;
/// Create a copy of WorkFlowDetailParams
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WorkFlowDetailParamsCopyWith<WorkFlowDetailParams> get copyWith => _$WorkFlowDetailParamsCopyWithImpl<WorkFlowDetailParams>(this as WorkFlowDetailParams, _$identity);

  /// Serializes this WorkFlowDetailParams to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WorkFlowDetailParams&&(identical(other.workflowId, workflowId) || other.workflowId == workflowId)&&(identical(other.withStartNode, withStartNode) || other.withStartNode == withStartNode));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,workflowId,withStartNode);

@override
String toString() {
  return 'WorkFlowDetailParams(workflowId: $workflowId, withStartNode: $withStartNode)';
}


}

/// @nodoc
abstract mixin class $WorkFlowDetailParamsCopyWith<$Res>  {
  factory $WorkFlowDetailParamsCopyWith(WorkFlowDetailParams value, $Res Function(WorkFlowDetailParams) _then) = _$WorkFlowDetailParamsCopyWithImpl;
@useResult
$Res call({
 String workflowId,@JsonKey(name: 'with_start_node') bool withStartNode
});




}
/// @nodoc
class _$WorkFlowDetailParamsCopyWithImpl<$Res>
    implements $WorkFlowDetailParamsCopyWith<$Res> {
  _$WorkFlowDetailParamsCopyWithImpl(this._self, this._then);

  final WorkFlowDetailParams _self;
  final $Res Function(WorkFlowDetailParams) _then;

/// Create a copy of WorkFlowDetailParams
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? workflowId = null,Object? withStartNode = null,}) {
  return _then(_self.copyWith(
workflowId: null == workflowId ? _self.workflowId : workflowId // ignore: cast_nullable_to_non_nullable
as String,withStartNode: null == withStartNode ? _self.withStartNode : withStartNode // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [WorkFlowDetailParams].
extension WorkFlowDetailParamsPatterns on WorkFlowDetailParams {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _WorkFlowDetailParams value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _WorkFlowDetailParams() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _WorkFlowDetailParams value)  $default,){
final _that = this;
switch (_that) {
case _WorkFlowDetailParams():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _WorkFlowDetailParams value)?  $default,){
final _that = this;
switch (_that) {
case _WorkFlowDetailParams() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String workflowId, @JsonKey(name: 'with_start_node')  bool withStartNode)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _WorkFlowDetailParams() when $default != null:
return $default(_that.workflowId,_that.withStartNode);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String workflowId, @JsonKey(name: 'with_start_node')  bool withStartNode)  $default,) {final _that = this;
switch (_that) {
case _WorkFlowDetailParams():
return $default(_that.workflowId,_that.withStartNode);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String workflowId, @JsonKey(name: 'with_start_node')  bool withStartNode)?  $default,) {final _that = this;
switch (_that) {
case _WorkFlowDetailParams() when $default != null:
return $default(_that.workflowId,_that.withStartNode);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable(createFactory: false)

class _WorkFlowDetailParams implements WorkFlowDetailParams {
   _WorkFlowDetailParams({required this.workflowId, @JsonKey(name: 'with_start_node') this.withStartNode = true});
  

@override final  String workflowId;
@override@JsonKey(name: 'with_start_node') final  bool withStartNode;

/// Create a copy of WorkFlowDetailParams
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WorkFlowDetailParamsCopyWith<_WorkFlowDetailParams> get copyWith => __$WorkFlowDetailParamsCopyWithImpl<_WorkFlowDetailParams>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WorkFlowDetailParamsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WorkFlowDetailParams&&(identical(other.workflowId, workflowId) || other.workflowId == workflowId)&&(identical(other.withStartNode, withStartNode) || other.withStartNode == withStartNode));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,workflowId,withStartNode);

@override
String toString() {
  return 'WorkFlowDetailParams(workflowId: $workflowId, withStartNode: $withStartNode)';
}


}

/// @nodoc
abstract mixin class _$WorkFlowDetailParamsCopyWith<$Res> implements $WorkFlowDetailParamsCopyWith<$Res> {
  factory _$WorkFlowDetailParamsCopyWith(_WorkFlowDetailParams value, $Res Function(_WorkFlowDetailParams) _then) = __$WorkFlowDetailParamsCopyWithImpl;
@override @useResult
$Res call({
 String workflowId,@JsonKey(name: 'with_start_node') bool withStartNode
});




}
/// @nodoc
class __$WorkFlowDetailParamsCopyWithImpl<$Res>
    implements _$WorkFlowDetailParamsCopyWith<$Res> {
  __$WorkFlowDetailParamsCopyWithImpl(this._self, this._then);

  final _WorkFlowDetailParams _self;
  final $Res Function(_WorkFlowDetailParams) _then;

/// Create a copy of WorkFlowDetailParams
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? workflowId = null,Object? withStartNode = null,}) {
  return _then(_WorkFlowDetailParams(
workflowId: null == workflowId ? _self.workflowId : workflowId // ignore: cast_nullable_to_non_nullable
as String,withStartNode: null == withStartNode ? _self.withStartNode : withStartNode // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
