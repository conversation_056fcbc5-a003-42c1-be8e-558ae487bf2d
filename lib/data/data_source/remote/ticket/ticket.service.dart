/*
 * Created Date: Thursday, 4th July 2024, 16:24:21
 * Author: gapo
 * -----
 * Last Modified: Friday, 11th April 2025 16:39:20
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:dio/dio.dart' hide Headers;
import 'package:injectable/injectable.dart';
import 'package:retrofit/retrofit.dart';

import '../../../../const/constants.dart';
import '../../../../const/url/ticket/ticket_url.constants.dart';

part 'ticket.service.g.dart';

@LazySingleton(order: DiConstants.kDataServiceOrder)
@Named('kTicketService')
@RestApi()
abstract class TicketService {
  @FactoryMethod()
  factory TicketService(
    @Named('kDio') Dio dio, {
    @Named('kTicketUrl') String? baseUrl,
  }) = _TicketService;

  /// update ngư<PERSON>i theo dõi cho tất cả các bước
  @POST('${TicketUrlConstants.kTicketUrl}/{ticketId}/follower')
  Future<void> addFollowersAllStep({
    @Path('ticketId') required String ticketId,
  });
}
