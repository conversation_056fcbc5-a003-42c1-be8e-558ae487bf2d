/*
 * Created Date: Tuesday, 22nd October 2024, 15:49:20
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 22nd October 2024 15:50:43
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

// ignore_for_file: public_member_api_docs
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:injectable/injectable.dart';

// import '../../../const/constants.dart';
import '../../domain.dart';

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class TicketAddFollowerAllStepUseCase
    extends GPBaseFutureUseCase<TicketAddFollowerInput, GPBaseOutput> {
  TicketAddFollowerAllStepUseCase(
    @Named('kTicketRepository') this._ticketRepository,
  );

  final TicketRepository _ticketRepository;

  @override
  Future<GPBaseOutput> buildUseCase(TicketAddFollowerInput input) async {
    await _ticketRepository.addFollowerAllStep(followerInput: input);
    return const TicketAddFollowerAllStepOutput();
  }
}

class TicketAddFollowerInput extends GPBaseInput {
  const TicketAddFollowerInput({required this.ticketId, required this.nodeId});

  final String ticketId;
  final String nodeId;
}

class TicketAddFollowerAllStepOutput extends GPBaseOutput {
  const TicketAddFollowerAllStepOutput();
}
