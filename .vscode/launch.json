{
    "version": "0.2.0",
    "configurations": [
        //=============== DEV FLAVOR ===============
        {
            "name": "Flavor: DEV (Debug)",
            "request": "launch",
            "type": "dart",
            "args": [
                "--flavor=staging",
                "--dart-define=ENV=staging",
                "--dart-define=ENV_FILE=.gapo/.env.dev",
            ]
        },
        {
            "name": "Flavor: DEV (Profile)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile",
            "args": [
                "--flavor=staging",
                "--dart-define=ENV=staging",
                "--dart-define=ENV_FILE=.gapo/.env.dev",
            ]
        },
        {
            "name": "Flavor: DEV (Release)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "args": [
                "--flavor=staging",
                "--dart-define=ENV=staging",
                "--dart-define=ENV_FILE=.gapo/.env.dev",
            ]
        },
        //=============== PROD FLAVOR ===============
        {
            "name": "Flavor: PROD (Debug)",
            "request": "launch",
            "type": "dart",
            "args": [
                "--flavor=prod",
                "--dart-define=ENV=prod",
                "--dart-define=ENV_FILE=.gapo/.env.prod",
            ]
        },
        {
            "name": "Flavor: PROD (Profile)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile",
            "args": [
                "--flavor=prod",
                "--dart-define=ENV=prod",
                "--dart-define=ENV_FILE=.gapo/.env.prod",
            ]
        },
        {
            "name": "Flavor: PROD (Release)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "args": [
                "--flavor=prod",
                "--dart-define=ENV=prod",
                "--dart-define=ENV_FILE=.gapo/.env.prod",
            ]
        }
    ]
}