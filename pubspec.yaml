_internal_variables_:
  - &gp_feature_ref "develop"
  - &gp_default_ref "develop"
  - &gp_module_ref "feat/3.22.2"

name: toannm_tech
description: "My flutter project with some awesome features"
publish_to: "none" # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: ^3.7.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

dependency_overrides:
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  get_it: 8.2.0
  flutter_dotenv: 6.0.0
  flutter_bloc: 9.1.1
  freezed_annotation: 3.1.0
  injectable: 2.5.1
  retrofit: 4.6.0
  dio: 5.9.00
  flutter_list_view: 1.1.29
  gp_core:
    git:
      url: **********************:flutter/core/gp_core.git
      ref: *gp_feature_ref
  gp_core_v2:
    git:
      url: **********************:flutter/core/gp_core_v2.git
      ref: "develop"
      path: gp_core_v2
  flutter_quill:
    git:
      url: **********************:flutter/rtfeditor.git
      ref: *gp_module_ref
  video_player: 2.9.5
  intl: ^0.19.0
  http: ^1.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  auto_mappr: 2.9.0
  build_runner: 2.5.4
  freezed: 3.1.0
  go_router_builder: 3.0.0
  injectable_generator: 2.7.0
  json_serializable: 6.9.5
  # mock_web_server: ^5.0.0-nullsafety.1
  pubspec_dependency_sorter: 1.0.5
  retrofit_generator: 9.7.0

flutter:
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - .gapo/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
